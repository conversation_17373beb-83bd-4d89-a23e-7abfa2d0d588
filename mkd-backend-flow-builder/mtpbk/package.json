{"name": "baas-v5-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "postinstall": "patch-package", "test": "node test_runner.js --path custom/core_backend/tests/lambda --pattern super_admin_login", "test:lambda": "node test_runner.js --path custom --verbose", "test:google": "node test_runner.js --path custom --pattern google --verbose", "test:generate": "node run_tests.js --name baas-v5 --generate", "gen:project": "node setup_baas_project.js --name pave --path C:\\Users\\<USER>\\Desktop\\Possible\\tech_space\\mkd\\baas_v5\\mtpbk\\config_files\\pave.config.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "apple-signin-auth": "^1.7.8", "aws-sdk": "^2.1414.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "image-size": "^1.2.1", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.14.2", "node-google-login": "^1.0.1", "nodemailer": "^6.10.1", "openai": "^4.96.0", "playwright": "^1.51.1", "qrcode": "^1.5.3", "redis": "^4.6.7", "sequelize": "^6.37.5", "speakeasy": "^2.0.0", "stripe": "^12.16.0", "twilio": "^5.6.0", "xml2js": "^0.6.2", "yargs": "^17.7.2"}, "devDependencies": {"nodemon": "^3.1.9", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}, "nodemonConfig": {"watch": ["./"], "ext": "js,json"}}