{"info": {"_postman_id": "thinkpartnership-marketplace-apis", "name": "ThinkPartnership Marketplace APIs", "description": "Complete API collection for ThinkPartnership Marketplace including Customer, Vendor, Client, and Admin portals", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set base URL if not already set", "if (!pm.environment.get('BASE_URL')) {", "    pm.environment.set('BASE_URL', 'https://baas.mytechpassport.com');", "}"]}}], "variable": [{"key": "BASE_URL", "value": "https://baas.mytechpassport.com", "type": "string"}], "item": [{"name": "Customer Portal APIs", "item": [{"name": "Authentication", "item": [{"name": "Customer Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"******-123-4567\",\n  \"address\": \"123 Main Street\",\n  \"city\": \"Toronto\",\n  \"province\": \"Ontario\",\n  \"postal_code\": \"M5V 3A8\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/customer/auth/register", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "customer", "auth", "register"]}}}, {"name": "Customer <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/customer/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "customer", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('CUSTOMER_TOKEN', response.token);", "    }", "    if (response.user_id) {", "        pm.environment.set('CUSTOMER_USER_ID', response.user_id);", "    }", "}"]}}]}, {"name": "Customer Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CUSTOMER_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/customer/auth/profile", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "customer", "auth", "profile"]}}}]}, {"name": "Services", "item": [{"name": "Get Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CUSTOMER_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/services?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "services"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Service Categories", "request": {"method": "GET", "url": {"raw": "{{BASE_URL}}/api/marketplace/categories", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "categories"]}}}]}, {"name": "Service Requests", "item": [{"name": "Create Service Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{CUSTOMER_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"service_id\": 1,\n  \"vendor_id\": 1,\n  \"description\": \"Need emergency plumbing repair for kitchen sink leak\",\n  \"preferred_date\": \"2024-02-15\",\n  \"budget_range\": \"$200-$500\",\n  \"location\": \"Toronto, ON\",\n  \"urgency\": \"high\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/customer/requests", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "customer", "requests"]}}}, {"name": "Get Customer Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CUSTOMER_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/customer/requests?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "customer", "requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Orders", "item": [{"name": "Get Customer Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CUSTOMER_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/orders", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "orders"]}}}]}]}, {"name": "🔧 Vendor Portal APIs", "item": [{"name": "Authentication", "item": [{"name": "Vendor Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"business_name\": \"ABC Plumbing Services\",\n  \"contact_name\": \"<PERSON>\",\n  \"phone\": \"******-123-4567\",\n  \"business_address\": \"123 Main Street\",\n  \"city\": \"Toronto\",\n  \"province\": \"Ontario\",\n  \"postal_code\": \"M5V 3A8\",\n  \"description\": \"Professional plumbing services with 10+ years experience\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/auth/register", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('VENDOR_TOKEN', response.token);", "    }", "    if (response.vendor_id) {", "        pm.environment.set('VENDOR_ID', response.vendor_id);", "    }", "}"]}}]}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('VENDOR_TOKEN', response.token);", "    }", "    if (response.vendor_id) {", "        pm.environment.set('VENDOR_ID', response.vendor_id);", "    }", "}"]}}]}, {"name": "Vendor Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/auth/profile", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "auth", "profile"]}}}]}, {"name": "Service Management", "item": [{"name": "Get Vendor Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/services?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "services"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Emergency Plumbing Repair\",\n  \"category_id\": 1,\n  \"description\": \"24/7 emergency plumbing services for leaks, clogs, and burst pipes\",\n  \"short_description\": \"Emergency plumbing repairs available 24/7\",\n  \"base_price\": 150.00,\n  \"features\": [\"24/7 availability\", \"Licensed plumbers\", \"1-year warranty\"],\n  \"tags\": [\"emergency\", \"plumbing\", \"24/7\"],\n  \"response_time\": \"30 minutes\",\n  \"delivery_time\": \"Same day\",\n  \"status\": 1\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/services", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "services"]}}}]}, {"name": "Request Management", "item": [{"name": "Get Vendor Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/requests?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Send Quote", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quote_amount\": 275.50,\n  \"quote_description\": \"Emergency pipe repair including parts and labor. Includes 1-year warranty.\",\n  \"valid_until\": \"2024-02-15\",\n  \"estimated_duration\": \"2-3 hours\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/requests/1/quote", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "requests", "1", "quote"]}}}]}, {"name": "Analytics", "item": [{"name": "Dashboard Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{VENDOR_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/vendor/analytics/dashboard", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "vendor", "analytics", "dashboard"]}}}]}]}, {"name": "🏢 Client Portal APIs", "item": [{"name": "Authentication", "item": [{"name": "Client Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"company_name\": \"TechCorp Marketplace\",\n  \"contact_name\": \"<PERSON>\",\n  \"phone\": \"******-987-6543\",\n  \"business_address\": \"456 Business Ave\",\n  \"city\": \"Toronto\",\n  \"province\": \"Ontario\",\n  \"postal_code\": \"M4B 1B3\",\n  \"business_type\": \"Technology Services\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/client/auth/register", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('CLIENT_TOKEN', response.token);", "    }", "    if (response.client_id) {", "        pm.environment.set('CLIENT_ID', response.client_id);", "    }", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/client/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('CLIENT_TOKEN', response.token);", "    }", "    if (response.client_id) {", "        pm.environment.set('CLIENT_ID', response.client_id);", "    }", "}"]}}]}]}, {"name": "Dashboard", "item": [{"name": "Dashboard Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/dashboard/stats", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "dashboard", "stats"]}}}]}, {"name": "Vendor Management", "item": [{"name": "Get Client Vendors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/vendors?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "vendors"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Update Vendor Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 1,\n  \"rejection_reason\": \"\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/client/vendors/1/status", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "vendors", "1", "status"]}}}]}, {"name": "Customer Management", "item": [{"name": "Get Client Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/customers", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "customers"]}}}]}, {"name": "Branding", "item": [{"name": "Get Branding Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/branding", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "branding"]}}}, {"name": "Update Branding Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"logo_url\": \"https://example.com/logo.png\",\n  \"primary_color\": \"#22C55E\",\n  \"secondary_color\": \"#3B82F6\",\n  \"font_family\": \"Inter, sans-serif\",\n  \"custom_domain\": \"marketplace.techcorp.com\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/client/branding", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "branding"]}}}]}, {"name": "Invite System", "item": [{"name": "Send Invites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"emails\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"invite_type\": \"vendor\",\n  \"message\": \"Join our marketplace as a service provider!\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/client/invites", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "invites"]}}}, {"name": "Get Invites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/invites", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "invites"]}}}]}, {"name": "Wallet", "item": [{"name": "Get Wallet Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/wallet", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "wallet"]}}}, {"name": "Get Transaction History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CLIENT_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/client/wallet/transactions?page=1&limit=20", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "client", "wallet", "transactions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}]}, {"name": "👑 Admin Portal APIs", "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('ADMIN_TOKEN', response.token);", "    }", "    if (response.user_id) {", "        pm.environment.set('ADMIN_USER_ID', response.user_id);", "    }", "}"]}}]}]}, {"name": "Dashboard", "item": [{"name": "Platform Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/dashboard/stats", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "dashboard", "stats"]}}}, {"name": "Revenue Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/analytics/revenue", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "analytics", "revenue"]}}}]}, {"name": "Client Management", "item": [{"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/clients?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "clients"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Update Client Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 1,\n  \"rejection_reason\": \"\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/clients/1/status", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "clients", "1", "status"]}}}]}, {"name": "Vendor Management", "item": [{"name": "Get All Vendors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/vendors?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "vendors"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Customer Management", "item": [{"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/customers?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "customers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Revenue Management", "item": [{"name": "Get Revenue Rules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/revenue/rules", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "revenue", "rules"]}}}, {"name": "Create/Update Revenue Rule", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"service_category\": \"Plumbing\",\n  \"platform_share\": 10.0,\n  \"default_client_share\": 10.0,\n  \"default_vendor_share\": 80.0\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/revenue/rules", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "revenue", "rules"]}}}, {"name": "Add Manual Commission", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"vendor_id\": 1,\n  \"amount\": 100.00,\n  \"description\": \"Manual commission adjustment for exceptional service\",\n  \"type\": \"manual_commission\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/commissions/manual", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "commissions", "manual"]}}}]}, {"name": "Reports & Analytics", "item": [{"name": "Performance Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/reports/performance?start_date=2024-01-01&end_date=2024-12-31", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "reports", "performance"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}, {"name": "Export Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/reports/export?type=transactions&start_date=2024-01-01&end_date=2024-12-31", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "reports", "export"], "query": [{"key": "type", "value": "transactions"}, {"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}]}, {"name": "System Management", "item": [{"name": "Get Service Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}], "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/categories", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "categories"]}}}, {"name": "Create Service Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electrical Services\",\n  \"description\": \"Professional electrical installation and repair services\",\n  \"icon\": \"electrical\",\n  \"sort_order\": 5\n}"}, "url": {"raw": "{{BASE_URL}}/api/marketplace/admin/categories", "host": ["{{BASE_URL}}"], "path": ["api", "marketplace", "admin", "categories"]}}}]}]}]}