# 🧪 Testing Custom Services Checkout

## Test Scenarios

### 1. Flat Fee Service Checkout
**Expected Behavior:**
- No quantity selector shown
- Shows fixed price
- Payment form shows service price only
- Order created with quantity = 1

**Test Steps:**
1. Go to marketplace
2. Select "Flat Fee" filter
3. Click on any flat fee service
4. Click "Request Service" → Should go to checkout
5. Verify no quantity selector is shown
6. Verify price shows as fixed amount
7. Proceed with checkout

### 2. Custom Service Checkout (Single Unit)
**Expected Behavior:**
- Quantity selector shown with min/max constraints
- Shows price per unit
- Default quantity = min_quantity
- Total = base_price × quantity

**Test Steps:**
1. Go to marketplace
2. Select "Custom Pricing" filter
3. Click on custom service (e.g., "Sha testing it" - ID 7)
4. Click "Request Service" → Should go to checkout
5. Verify quantity selector is shown
6. Verify default quantity = min_quantity (1)
7. Verify price shows "$20.00/service"
8. Verify total shows "$20.00"

### 3. Custom Service Checkout (Multiple Units)
**Expected Behavior:**
- Can increase/decrease quantity
- Total updates dynamically
- Payment form shows correct total
- Order created with correct quantity

**Test Steps:**
1. Continue from Test 2
2. Click "+" button to increase quantity to 3
3. Verify quantity field shows "3"
4. Verify total updates to "$60.00" (20 × 3)
5. Verify payment form shows:
   - Service: "Sha testing it (3 services)"
   - Breakdown: "3 services × $20.00 each"
   - Total: $60.00 + fees
6. Proceed with checkout

### 4. Quantity Constraints
**Expected Behavior:**
- Cannot go below min_quantity
- Cannot go above max_quantity (if set)
- Buttons disabled appropriately

**Test Steps:**
1. Try to decrease quantity below minimum
2. Verify "-" button is disabled at minimum
3. If max_quantity is set, try to increase above maximum
4. Verify "+" button is disabled at maximum

## Expected UI Elements

### Checkout Page for Custom Services:
```
┌─────────────────────────────────────┐
│ Service: Sha testing it             │
│                                     │
│ Quantity (services)                 │
│ [-] [3] [+]                        │
│ Minimum quantity: 1 services        │
│                                     │
│ Price per service: $20.00           │
│ Quantity: 3 services                │
│ ─────────────────────────────       │
│ Total Amount: $60.00                │
└─────────────────────────────────────┘
```

### Payment Form for Custom Services:
```
┌─────────────────────────────────────┐
│ Order Summary                       │
│                                     │
│ Sha testing it (3 services)        │
│ 3 services × $20.00 each    $60.00 │
│                                     │
│ Service Fee                   $5.00 │
│ Processing Fee               $2.50  │
│ ─────────────────────────────       │
│ Total                       $67.50  │
└─────────────────────────────────────┘
```

## Backend Requirements

The backend should handle:
- Service details endpoint returning custom service fields
- Order creation with quantity and unit_type
- Payment processing with correct total amount

## Files Modified

1. **CheckoutPage.tsx** - Added quantity selector and total calculation
2. **PaymentForm.tsx** - Updated to handle quantity and show breakdown
3. **ServicesAPI.ts** - Updated interfaces for custom services
4. **marketplace_api.js** - Updated service details endpoint

## Deployment Checklist

- [ ] Backend API deployed with custom service support
- [ ] Frontend deployed with quantity selector
- [ ] Test flat fee service checkout
- [ ] Test custom service checkout (single unit)
- [ ] Test custom service checkout (multiple units)
- [ ] Test quantity constraints
- [ ] Verify order creation includes quantity info
- [ ] Verify payment processing with correct totals
